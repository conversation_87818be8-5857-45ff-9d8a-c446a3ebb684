import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, MapPin, Users, Mail, Phone, User, GraduationCap, Shield, Lock, CreditCard, QrCode, ArrowLeft } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Link } from "react-router-dom";
import emailjs from '@emailjs/browser';
import { EMAILJS_CONFIG } from "@/config/emailjs";

const Registration = () => {
  const { toast } = useToast();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    college: "",
    year: "",
    branch: "",
    experience: "",
    expectations: "",
    transactionId: "",
    utrCode: "",
    paymentMethod: ""
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [captcha, setCaptcha] = useState({
    imageNumbers: [] as number[],
    correctAnswer: "",
    userAnswer: ""
  });
  const [showPayment, setShowPayment] = useState(false);
  const [paymentCompleted, setPaymentCompleted] = useState(false);
  const [captchaTimer, setCaptchaTimer] = useState(300); // 5 minutes in seconds
  const [registrationId, setRegistrationId] = useState("");

  // Generate a unique registration ID: CY01 + 16 digits
  const generateRegistrationId = () => {
    const randomDigits = Array.from({ length: 16 }, () => Math.floor(Math.random() * 10)).join("");
    return `CY01${randomDigits}`;
  };

  // Security: Disable right-click and keyboard shortcuts
  useEffect(() => {
    const disableRightClick = (e: MouseEvent) => e.preventDefault();
    const disableKeyboard = (e: KeyboardEvent) => {
      if (e.key === 'F12' || 
          (e.ctrlKey && e.shiftKey && e.key === 'I') ||
          (e.ctrlKey && e.shiftKey && e.key === 'C') ||
          (e.ctrlKey && e.key === 'u') ||
          (e.ctrlKey && e.key === 's')) {
        e.preventDefault();
      }
    };

    document.addEventListener('contextmenu', disableRightClick);
    document.addEventListener('keydown', disableKeyboard);

    return () => {
      document.removeEventListener('contextmenu', disableRightClick);
      document.removeEventListener('keydown', disableKeyboard);
    };
  }, []);

  // Generate Image-based CAPTCHA
  useEffect(() => {
    generateCaptcha();
  }, []);

  // CAPTCHA Timer
  useEffect(() => {
    const timer = setInterval(() => {
      setCaptchaTimer(prev => {
        if (prev <= 1) {
          generateCaptcha();
          return 300; // Reset to 5 minutes
        }
        return prev - 1;
      });
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  const generateCaptcha = () => {
    // Generate 4 random numbers for image-based CAPTCHA
    const numbers = Array.from({ length: 4 }, () => Math.floor(Math.random() * 10));
    const answer = numbers.join('');

    setCaptcha({
      imageNumbers: numbers,
      correctAnswer: answer,
      userAnswer: ""
    });
    setCaptchaTimer(300); // Reset timer
  };

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const initiatePayment = (method: string, upiLink: string) => {
    setFormData(prev => ({ ...prev, paymentMethod: method }));
    
    // Show payment loader
    const button = document.querySelector(`.${method}-btn .payment-loader`) as HTMLElement;
    if (button) button.style.display = 'block';
    
    // Open UPI app
    window.location.href = upiLink;
    
    setTimeout(() => {
      if (button) button.style.display = 'none';
      setShowPayment(true);
    }, 3000);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate CAPTCHA
    if (captcha.userAnswer !== captcha.correctAnswer) {
      toast({
        title: "CAPTCHA Failed",
        description: "Please enter the correct numbers from the image.",
        variant: "destructive",
      });
      generateCaptcha();
      return;
    }

    // Validate payment completion
    if (!paymentCompleted || !formData.transactionId || !formData.utrCode) {
      toast({
        title: "Payment Required",
        description: "Please complete payment and provide transaction details.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // Generate registration ID
      const newRegistrationId = generateRegistrationId();
      setRegistrationId(newRegistrationId);

      // EmailJS configuration
      const templateParams = {
        to_email: '<EMAIL>',
        from_name: formData.fullName,
        from_email: formData.email,
        phone: formData.phone,
        college: formData.college,
        year: formData.year,
        branch: formData.branch,
        experience: formData.experience,
        expectations: formData.expectations,
        transaction_id: formData.transactionId,
        utr_code: formData.utrCode,
        payment_method: formData.paymentMethod,
        registration_date: new Date().toLocaleString(),
        registration_id: newRegistrationId
      };

      // Send email using EmailJS
      await emailjs.send(
        EMAILJS_CONFIG.SERVICE_ID,
        EMAILJS_CONFIG.TEMPLATE_ID,
        templateParams,
        EMAILJS_CONFIG.PUBLIC_KEY
      );

      toast({
        title: "Registration Successful!",
        description: "Redirecting to success page...",
      });

      // Redirect to success page with data after 2 seconds
      setTimeout(() => {
        const params = new URLSearchParams({
          id: newRegistrationId,
          name: formData.fullName,
          transactionId: formData.transactionId,
          utrCode: formData.utrCode,
          timestamp: new Date().toLocaleString()
        });
        window.location.href = `/registration-success?${params.toString()}`;
      }, 2000);

    } catch (error) {
      toast({
        title: "Registration Failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-black py-20">
      {/* Security overlay to prevent inspection */}
      <div className="fixed inset-0 pointer-events-none z-50" style={{ userSelect: 'none' }}>
        <div className="absolute inset-0 bg-transparent"></div>
      </div>
      
      <div className="container px-4 relative z-10">
        {/* Back Button */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <Link to="/">
            <Button
              variant="outline"
              className="border-gray-600 text-white hover:bg-gray-800 hover:border-gray-500 flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Home
            </Button>
          </Link>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="max-w-4xl mx-auto"
        >
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white">Workshop </span>
              <span className="text-red-400">Registration</span>
            </h1>
            <p className="text-xl text-gray-300">
              Secure your spot in the Cyber Wolf CTF Workshop
            </p>
            <div className="mt-4 inline-flex items-center gap-2 bg-green-500/20 px-4 py-2 rounded-full">
              <Shield className="w-5 h-5 text-green-400" />
              <span className="text-green-400 font-semibold">Secure Registration</span>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Registration Form */}
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <User className="w-5 h-5 text-red-400" />
                  Registration Details
                </CardTitle>
                <CardDescription className="text-gray-300">
                  Fill in your information to register for the workshop
                </CardDescription>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="fullName" className="text-white">Full Name *</Label>
                      <Input
                        id="fullName"
                        value={formData.fullName}
                        onChange={(e) => handleInputChange("fullName", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-white">Email *</Label>
                      <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={(e) => handleInputChange("email", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone" className="text-white">Phone Number *</Label>
                      <Input
                        id="phone"
                        value={formData.phone}
                        onChange={(e) => handleInputChange("phone", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        required
                      />
                    </div>
                    <div>
                      <Label htmlFor="college" className="text-white">College/Institution *</Label>
                      <Input
                        id="college"
                        value={formData.college}
                        onChange={(e) => handleInputChange("college", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="year" className="text-white">Year of Study</Label>
                      <Select onValueChange={(value) => handleInputChange("year", value)}>
                        <SelectTrigger className="bg-black/50 border-gray-600 text-white">
                          <SelectValue placeholder="Select year" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1st">1st Year</SelectItem>
                          <SelectItem value="2nd">2nd Year</SelectItem>
                          <SelectItem value="3rd">3rd Year</SelectItem>
                          <SelectItem value="4th">4th Year</SelectItem>
                          <SelectItem value="graduate">Graduate</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="branch" className="text-white">Branch/Department</Label>
                      <Input
                        id="branch"
                        value={formData.branch}
                        onChange={(e) => handleInputChange("branch", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        placeholder="e.g., Computer Science"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="experience" className="text-white">Cybersecurity Experience</Label>
                    <Select onValueChange={(value) => handleInputChange("experience", value)}>
                      <SelectTrigger className="bg-black/50 border-gray-600 text-white">
                        <SelectValue placeholder="Select experience level" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="beginner">Complete Beginner</SelectItem>
                        <SelectItem value="basic">Basic Knowledge</SelectItem>
                        <SelectItem value="intermediate">Intermediate</SelectItem>
                        <SelectItem value="advanced">Advanced</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div>
                    <Label htmlFor="expectations" className="text-white">What do you hope to learn?</Label>
                    <Textarea
                      id="expectations"
                      value={formData.expectations}
                      onChange={(e) => handleInputChange("expectations", e.target.value)}
                      className="bg-black/50 border-gray-600 text-white"
                      placeholder="Tell us about your learning goals..."
                      rows={3}
                    />
                  </div>

                  {/* Image-based CAPTCHA */}
                  <div className="bg-gray-800/50 p-4 rounded-lg border border-gray-600">
                    <Label className="text-white mb-2 block">Security Verification *</Label>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <span className="text-white text-sm">Enter the numbers you see below:</span>
                        <span className="text-green-400 text-sm font-mono">
                          Time: {formatTime(captchaTimer)}
                        </span>
                      </div>

                      {/* CAPTCHA Image Numbers */}
                      <div className="flex items-center justify-center gap-2 bg-white p-4 rounded-lg">
                        {captcha.imageNumbers.map((number, index) => (
                          <div
                            key={index}
                            className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white font-bold text-xl shadow-lg transform rotate-1 hover:rotate-0 transition-transform"
                            style={{
                              transform: `rotate(${(index % 2 === 0 ? 1 : -1) * (index + 1) * 3}deg)`,
                              background: `linear-gradient(${45 + index * 30}deg,
                                hsl(${200 + index * 40}, 70%, 50%),
                                hsl(${250 + index * 30}, 80%, 60%))`
                            }}
                          >
                            {number}
                          </div>
                        ))}
                      </div>

                      <div className="flex items-center gap-4">
                        <Input
                          type="text"
                          value={captcha.userAnswer}
                          onChange={(e) => setCaptcha(prev => ({ ...prev, userAnswer: e.target.value }))}
                          className="bg-black/50 border-gray-600 text-white"
                          placeholder="Enter the 4 numbers"
                          maxLength={4}
                          required
                        />
                        <Button
                          type="button"
                          onClick={generateCaptcha}
                          variant="outline"
                          size="sm"
                          className="border-gray-600 hover:bg-gray-700"
                        >
                          <i className="fas fa-refresh mr-2"></i>
                          Refresh
                        </Button>
                      </div>

                      <p className="text-xs text-gray-400">
                        CAPTCHA refreshes automatically every 5 minutes for security
                      </p>
                    </div>
                  </div>

                  {/* Payment Section */}
                  {showPayment && (
                    <div className="space-y-4">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="transactionId" className="text-white">Transaction ID *</Label>
                          <Input
                            id="transactionId"
                            value={formData.transactionId}
                            onChange={(e) => handleInputChange("transactionId", e.target.value)}
                            className="bg-black/50 border-gray-600 text-white"
                            placeholder="Enter transaction ID"
                            required
                          />
                        </div>
                        <div>
                          <Label htmlFor="utrCode" className="text-white">UTR Code *</Label>
                          <Input
                            id="utrCode"
                            value={formData.utrCode}
                            onChange={(e) => handleInputChange("utrCode", e.target.value)}
                            className="bg-black/50 border-gray-600 text-white"
                            placeholder="Enter UTR code"
                            required
                          />
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          id="paymentConfirm"
                          checked={paymentCompleted}
                          onChange={(e) => setPaymentCompleted(e.target.checked)}
                          className="rounded"
                        />
                        <Label htmlFor="paymentConfirm" className="text-white">
                          I confirm that payment has been completed
                        </Label>
                      </div>
                    </div>
                  )}

                  <Button 
                    type="submit" 
                    className="w-full button-gradient" 
                    disabled={isSubmitting || (!paymentCompleted && showPayment)}
                  >
                    {isSubmitting ? "Submitting..." : "Complete Registration"}
                  </Button>
                </form>
              </CardContent>
            </Card>

            {/* Payment Options */}
            <div className="space-y-6">
              {/* Workshop Details */}
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-red-400" />
                    Workshop Details
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center">
                    <div className="text-3xl font-bold text-green-400 mb-2">₹250 Only</div>
                    <div className="text-gray-300">2 Days Workshop</div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-blue-400" />
                      <span className="text-gray-300">SKP Engineering College</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-green-400" />
                      <span className="text-gray-300"><EMAIL></span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-yellow-400" />
                      <span className="text-gray-300">6374344424, 9790155280</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* Payment Methods */}
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <CreditCard className="w-5 h-5 text-red-400" />
                    Payment Options
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Choose your preferred payment method
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mb-6">
                    {/* Google Pay */}
                    <button
                      className="pay-button googlepay-btn bg-gradient-to-r from-blue-500 to-green-500 hover:from-blue-600 hover:to-green-600 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('googlepay', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <div className="text-2xl font-bold">G</div>
                      <span className="font-semibold text-sm">Google Pay</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>

                    {/* PhonePe */}
                    <button
                      className="pay-button phonepe-btn bg-gradient-to-r from-purple-600 to-purple-800 hover:from-purple-700 hover:to-purple-900 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('phonepe', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <div className="text-2xl font-bold">₹</div>
                      <span className="font-semibold text-sm">PhonePe</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>

                    {/* BHIM UPI */}
                    <button
                      className="pay-button bhim-btn bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('bhim', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <i className="fas fa-university text-xl"></i>
                      <span className="font-semibold text-sm">BHIM UPI</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>

                    {/* MobiKwik */}
                    <button
                      className="pay-button mobikwik-btn bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('mobikwik', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <i className="fas fa-mobile text-xl"></i>
                      <span className="font-semibold text-sm">MobiKwik</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>

                    {/* FreeCharge */}
                    <button
                      className="pay-button freecharge-btn bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('freecharge', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <i className="fas fa-bolt text-xl"></i>
                      <span className="font-semibold text-sm">FreeCharge</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>

                    {/* Generic UPI */}
                    <button
                      className="pay-button upi-btn bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg transition-colors flex flex-col items-center gap-2"
                      onClick={() => initiatePayment('upi', 'upi://pay?pa=e-bookshp-programming@axl&pn=CyberWolf%20CTF&am=250&cu=INR&tn=CTF%20Registration')}
                    >
                      <i className="fas fa-credit-card text-xl"></i>
                      <span className="font-semibold text-sm">Other UPI</span>
                      <small>₹250</small>
                      <div className="payment-loader hidden">
                        <i className="fas fa-spinner fa-spin"></i>
                      </div>
                    </button>
                  </div>

                  {/* QR Code */}
                  <div className="text-center">
                    <h4 className="text-white font-semibold mb-4 flex items-center justify-center gap-2">
                      <QrCode className="w-5 h-5" />
                      Or Scan QR Code
                    </h4>
                    <div className="bg-white p-4 rounded-lg inline-block">
                      <img 
                        src="https://ctf-hackathon-cyberwolf.web.app/images/qr/WhatsApp%20Image%202025-05-26%20at%2011.01.17%20PM.jpeg"
                        alt="Payment QR Code"
                        className="w-48 h-48 object-contain"
                      />
                    </div>
                    <p className="text-gray-400 text-sm mt-2">
                      Scan with any UPI app to pay ₹250
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Registration;

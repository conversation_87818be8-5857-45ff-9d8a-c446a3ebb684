import { motion } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Home, ArrowLeft, Search, Shield, AlertTriangle } from "lucide-react";
import { Link } from "react-router-dom";

const NotFound = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black flex items-center justify-center relative overflow-hidden">
      {/* Animated Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-red-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-60 h-60 bg-green-500/5 rounded-full blur-2xl animate-pulse delay-500"></div>
      </div>

      {/* Floating Security Icons */}
      <div className="absolute inset-0 pointer-events-none">
        <motion.div
          animate={{ 
            y: [0, -20, 0],
            rotate: [0, 5, -5, 0]
          }}
          transition={{ 
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          className="absolute top-20 left-20 text-red-400/30"
        >
          <Shield className="w-8 h-8" />
        </motion.div>
        
        <motion.div
          animate={{ 
            y: [0, 15, 0],
            rotate: [0, -5, 5, 0]
          }}
          transition={{ 
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
          className="absolute top-40 right-32 text-blue-400/30"
        >
          <AlertTriangle className="w-6 h-6" />
        </motion.div>

        <motion.div
          animate={{ 
            y: [0, -10, 0],
            x: [0, 10, 0]
          }}
          transition={{ 
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 2
          }}
          className="absolute bottom-32 left-32 text-green-400/30"
        >
          <Search className="w-7 h-7" />
        </motion.div>
      </div>

      <div className="container px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8 }}
          className="max-w-4xl mx-auto text-center"
        >
          {/* 404 Illustration with Images */}
          <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="mb-8"
          >
            <div className="relative flex flex-col items-center">
              {/* Cybersecurity Character Image */}
              <motion.div
                initial={{ scale: 0.8, opacity: 0 }}
                animate={{ scale: 1, opacity: 1 }}
                transition={{ duration: 1, delay: 0.3 }}
                className="mb-6"
              >
                <div className="relative">
                  <img
                    src="/images/3drenderingcartoonlikemanworkingcomputer_884224-2312-Photoroom.png"
                    alt="Cybersecurity Expert"
                    className="w-48 h-48 md:w-64 md:h-64 object-contain mx-auto filter drop-shadow-2xl"
                  />
                  {/* Glowing effect around image */}
                  <div className="absolute inset-0 bg-gradient-to-r from-red-500/20 to-blue-500/20 rounded-full blur-xl animate-pulse"></div>
                </div>
              </motion.div>

              {/* Main 404 Text */}
              <motion.h1
                className="text-[8rem] md:text-[12rem] font-bold text-transparent bg-gradient-to-r from-red-500 via-red-400 to-red-600 bg-clip-text leading-none mb-4"
                animate={{
                  textShadow: [
                    "0 0 20px rgba(239, 68, 68, 0.3)",
                    "0 0 40px rgba(239, 68, 68, 0.5)",
                    "0 0 20px rgba(239, 68, 68, 0.3)"
                  ]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  ease: "easeInOut"
                }}
              >
                404
              </motion.h1>

              {/* Glitch Effect Overlay */}
              <motion.div
                className="absolute bottom-0 text-[8rem] md:text-[12rem] font-bold text-blue-400/20 leading-none"
                animate={{
                  x: [0, 2, -2, 0],
                  opacity: [0, 0.3, 0, 0.2, 0]
                }}
                transition={{
                  duration: 0.3,
                  repeat: Infinity,
                  repeatDelay: 3
                }}
              >
                404
              </motion.div>

              {/* Floating Security Elements */}
              <motion.div
                className="absolute -top-10 -left-10"
                animate={{
                  rotate: 360,
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  rotate: { duration: 20, repeat: Infinity, ease: "linear" },
                  scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
                }}
              >
                <img
                  src="/images/3d-rendering-red-flag-isolated-white-background_1121794-716-Photoroom.png"
                  alt="Security Flag"
                  className="w-16 h-16 md:w-20 md:h-20 object-contain opacity-70"
                />
              </motion.div>

              <motion.div
                className="absolute -top-5 -right-10"
                animate={{
                  y: [0, -10, 0],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 1
                }}
              >
                <img
                  src="/images/ctf.png"
                  alt="CTF Badge"
                  className="w-12 h-12 md:w-16 md:h-16 object-contain opacity-60"
                />
              </motion.div>
            </div>
          </motion.div>

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="mb-8"
          >
            <h2 className="text-3xl md:text-5xl font-bold text-white mb-4">
              Page Not Found
            </h2>
            <p className="text-lg md:text-xl text-gray-300 mb-2">
              Looks like this page has been <span className="text-red-400 font-semibold">compromised</span> or doesn't exist.
            </p>
            <p className="text-gray-400">
              Don't worry, our cybersecurity experts are on it! 🛡️
            </p>
          </motion.div>

          {/* Info Card with Visual Elements */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mb-8"
          >
            <Card className="glass-enhanced border-red-500/30 max-w-3xl mx-auto relative overflow-hidden">
              <CardContent className="p-6 relative">
                {/* Background Pattern */}
                <div className="absolute inset-0 opacity-5">
                  <div className="absolute top-4 right-4">
                    <img
                      src="/images/man-sits-laptop-with-word-web-screen_1181564-2976-Photoroom.png"
                      alt="Web Developer"
                      className="w-32 h-32 object-contain"
                    />
                  </div>
                </div>

                <div className="relative z-10">
                  <div className="flex items-center justify-center gap-3 mb-4">
                    <motion.div
                      animate={{ rotate: [0, 360] }}
                      transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
                    >
                      <Shield className="w-6 h-6 text-red-400" />
                    </motion.div>
                    <h3 className="text-xl font-semibold text-white">Security Alert</h3>
                  </div>

                  <div className="grid md:grid-cols-2 gap-6 items-center">
                    <div>
                      <p className="text-gray-300 text-sm leading-relaxed mb-3">
                        The requested resource could not be located. This might be due to:
                      </p>
                      <ul className="text-gray-400 text-sm space-y-1">
                        <li className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          Incorrect URL or broken link
                        </li>
                        <li className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          Page has been moved or deleted
                        </li>
                        <li className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          Access permissions required
                        </li>
                        <li className="flex items-center gap-2">
                          <div className="w-1 h-1 bg-red-400 rounded-full"></div>
                          Temporary server maintenance
                        </li>
                      </ul>
                    </div>

                    <div className="text-center">
                      <motion.div
                        animate={{
                          scale: [1, 1.05, 1],
                          rotate: [0, 2, -2, 0]
                        }}
                        transition={{
                          duration: 4,
                          repeat: Infinity,
                          ease: "easeInOut"
                        }}
                        className="inline-block"
                      >
                        <img
                          src="/images/certificate.png"
                          alt="Cybersecurity Certificate"
                          className="w-24 h-24 object-contain mx-auto opacity-80"
                        />
                      </motion.div>
                      <p className="text-xs text-gray-500 mt-2">
                        Join our cybersecurity workshop instead!
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link to="/">
              <Button size="lg" className="button-gradient group">
                <Home className="mr-2 w-5 h-5 group-hover:scale-110 transition-transform" />
                Back to Home
              </Button>
            </Link>
            
            <Button 
              size="lg" 
              variant="outline" 
              className="border-red-400 text-red-400 hover:bg-red-400 hover:text-white group"
              onClick={() => window.history.back()}
            >
              <ArrowLeft className="mr-2 w-5 h-5 group-hover:scale-110 transition-transform" />
              Go Back
            </Button>

            <Link to="/register">
              <Button 
                size="lg" 
                variant="outline" 
                className="border-green-400 text-green-400 hover:bg-green-400 hover:text-white group"
              >
                <Shield className="mr-2 w-5 h-5 group-hover:scale-110 transition-transform" />
                Join Workshop
              </Button>
            </Link>
          </motion.div>

          {/* Additional Help */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="mt-12 text-center"
          >
            <p className="text-gray-500 text-sm">
              Need help? Contact us at{" "}
              <a 
                href="mailto:<EMAIL>" 
                className="text-blue-400 hover:text-blue-300 underline transition-colors"
              >
                <EMAIL>
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default NotFound;

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { FeatureTab } from "./FeatureTab";
import { FeatureContent } from "./FeatureContent";
import { features } from "@/config/features";

export const FeaturesSection = () => {
  return (
    <section className="container px-4 py-16 md:py-24 lg:py-32">
      {/* Header Section */}
      <div className="max-w-4xl mb-12 md:mb-20 text-center md:text-left">
        <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-normal mb-4 md:mb-6 tracking-tight">
          Workshop
          <br />
          <span className="text-gradient font-medium">Benefits & Learning</span>
        </h2>
        <p className="text-base sm:text-lg md:text-xl text-gray-400 max-w-3xl mx-auto md:mx-0">
          Discover what makes our cybersecurity workshop unique and how it will transform your understanding of digital security.
        </p>
      </div>

      <Tabs defaultValue={features[0].title} className="w-full">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 md:gap-8 lg:gap-12">
          {/* Left side - Tab triggers */}
          <div className="lg:col-span-5 order-2 lg:order-1">
            <TabsList className="flex flex-col w-full bg-transparent h-auto p-0 space-y-2 md:space-y-3">
              {features.map((feature) => (
                <TabsTrigger
                  key={feature.title}
                  value={feature.title}
                  className="w-full data-[state=active]:shadow-none data-[state=active]:bg-transparent"
                >
                  <FeatureTab
                    title={feature.title}
                    description={feature.description}
                    icon={feature.icon}
                    isActive={false}
                  />
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {/* Right side - Tab content with images */}
          <div className="lg:col-span-7 order-1 lg:order-2">
            {features.map((feature) => (
              <TabsContent
                key={feature.title}
                value={feature.title}
                className="mt-0 h-full"
              >
                <FeatureContent
                  image={feature.image}
                  title={feature.title}
                />
              </TabsContent>
            ))}
          </div>
        </div>
      </Tabs>
    </section>
  );
};
import { motion } from "framer-motion";
import { Shield, Code, Users, Target, BookOpen, Zap } from "lucide-react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import ScheduleSection from "./features/ScheduleSection";

const WorkshopContent = () => {
  const curriculumItems = [
    {
      icon: Shield,
      title: "Cybersecurity Fundamentals",
      description: "Learn the core principles of cybersecurity, threat landscape, and defense mechanisms",
      topics: ["Network Security", "Cryptography Basics", "Security Policies", "Risk Assessment"]
    },
    {
      icon: Target,
      title: "CTF Methodology",
      description: "Master the art of Capture The Flag competitions with proven strategies and techniques",
      topics: ["Web Exploitation", "Binary Analysis", "Forensics", "Steganography"]
    },
    {
      icon: Code,
      title: "Programming for Security",
      description: "Develop coding skills essential for cybersecurity professionals",
      topics: ["Python Scripting", "JavaScript Security", "SQL Injection", "Secure Coding"]
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Learn to work effectively in cybersecurity teams during competitions",
      topics: ["Communication", "Task Division", "Time Management", "Knowledge Sharing"]
    },
    {
      icon: BookOpen,
      title: "Practical Labs",
      description: "Hands-on experience with real-world cybersecurity scenarios",
      topics: ["Vulnerability Assessment", "Penetration Testing", "Incident Response", "Tool Usage"]
    },
    {
      icon: Zap,
      title: "Advanced Techniques",
      description: "Explore cutting-edge cybersecurity methods and emerging threats",
      topics: ["AI in Security", "IoT Security", "Cloud Security", "Mobile Security"]
    }
  ];

  const programmingLanguages = [
    {
      name: "Python",
      icon: "https://nature-basket-online.web.app/public/logo/download-Photoroom.png"
    },
    {
      name: "HTML",
      icon: "https://nature-basket-online.web.app/public/logo/download (1)-Photoroom.png"
    },
    {
      name: "Shell",
      icon: "https://nature-basket-online.web.app/public/logo/images-Photoroom.png"
    },
    {
      name: "Java",
      icon: "https://nature-basket-online.web.app/public/logo/java-programming-language-logo-transparent-k6ngrbl1m2xbzz4t-k6ngrbl1m2xbzz4t-Photoroom.png"
    },
     {
      name: "HTML",
      icon: "https://nature-basket-online.web.app/public/logo/download (1)-Photoroom.png"
    }
  ];

  return (
    <>
      <section className="py-20 bg-black">
        <div className="container px-4">
          {/* Workshop Overview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-16"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white">Workshop </span>
              <span className="text-red-400">Curriculum</span>
            </h2>
            
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              A comprehensive two-day intensive program designed to transform beginners into cybersecurity enthusiasts 
              with practical skills and real-world experience.
            </p>
            <motion.div
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.6, delay: 0.9 }}
                className="flex justify-center"
              >
                <img
                  src="images/Screenshot 2025-06-17 155634-Photoroom.png"
                  alt="3D Character Working on Computer - Cybersecurity Learning"
                  className="w-full h-auto object-contain rounded-2xl max-w-lg"
                  loading="lazy"
                />
              </motion.div>
          </motion.div>

          {/* Curriculum Grid */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
          >
            {curriculumItems.map((item, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <Card className="bg-gray-900/50 border-gray-700 hover:border-red-400/50 transition-colors h-full">
                  <CardHeader>
                    <div className="flex items-center gap-3 mb-2">
                      <div className="p-2 bg-red-500/20 rounded-lg">
                        <item.icon className="w-6 h-6 text-red-400" />
                      </div>
                      <CardTitle className="text-white">{item.title}</CardTitle>
                    </div>
                    <CardDescription className="text-gray-300">
                      {item.description}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {item.topics.map((topic, topicIndex) => (
                        <li key={topicIndex} className="text-gray-400 text-sm flex items-center">
                          <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                          {topic}
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>

          {/* Programming Languages Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-center overflow-hidden"
          >
            <h3 className="text-3xl font-bold mb-6 text-white">
              Programming Languages <span className="text-blue-400">Covered</span>
            </h3>
            <p className="text-gray-300 mb-8 max-w-2xl mx-auto">
              Master multiple programming languages essential for cybersecurity professionals
            </p>

            {/* Scrolling Languages Container */}
            <div className="relative flex overflow-hidden py-4">
              <div className="animate-marquee-reverse flex min-w-full shrink-0 items-stretch gap-6">
                {programmingLanguages.map((lang, index) => (
                  <motion.div
                    key={`${index}-1`}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="w-[220px] shrink-0 bg-gray-900/50 border border-gray-700 rounded-xl p-6 hover:border-blue-400/50 hover:bg-gray-800/50 transition-all duration-300 transform hover:scale-105"
                  >
                    <div className="w-24 h-24 rounded-xl mx-auto mb-4 flex items-center justify-center shadow-lg transform hover:rotate-6 transition-transform duration-300">
                      <img src={lang.icon} alt={lang.name + ' logo'} className="w-20 h-20 object-contain" />
                    </div>
                    <h4 className="font-bold mb-2 text-lg text-white">{lang.name}</h4>
                  </motion.div>
                ))}
              </div>
              <div className="animate-marquee-reverse flex min-w-full shrink-0 items-stretch gap-6">
                {programmingLanguages.map((lang, index) => (
                  <motion.div
                    key={`${index}-2`}
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3, delay: index * 0.1 }}
                    className="w-[220px] shrink-0 bg-gray-900/50 border border-gray-700 rounded-xl p-6 hover:border-blue-400/50 hover:bg-gray-800/50 transition-all duration-300 transform hover:scale-105"
                  >
                    <div className="w-24 h-24 rounded-xl mx-auto mb-4 flex items-center justify-center shadow-lg transform hover:rotate-6 transition-transform duration-300">
                      <img src={lang.icon} alt={lang.name + ' logo'} className="w-20 h-20 object-contain" />
                    </div>
                    <h4 className="font-bold mb-2 text-lg text-white">{lang.name}</h4>
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>
        </div>
      </section>
      <ScheduleSection />
    </>
  );
};

export default WorkshopContent;

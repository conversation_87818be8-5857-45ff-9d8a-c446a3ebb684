<svg width="400" height="600" viewBox="0 0 400 600" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bookGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#b91c1c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e40af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="shadowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#000000;stop-opacity:0.3" />
      <stop offset="100%" style="stop-color:#000000;stop-opacity:0.1" />
    </linearGradient>
    <filter id="glow">
      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
      <feMerge> 
        <feMergeNode in="coloredBlur"/>
        <feMergeNode in="SourceGraphic"/>
      </feMerge>
    </filter>
  </defs>
  
  <!-- Background -->
  <rect width="400" height="600" fill="#0a0a0a"/>
  
  <!-- Book Shadow -->
  <rect x="25" y="25" width="350" height="550" rx="15" fill="url(#shadowGradient)"/>
  
  <!-- Book Cover -->
  <rect x="20" y="20" width="350" height="550" rx="15" fill="url(#bookGradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Inner Border -->
  <rect x="35" y="35" width="320" height="520" rx="10" fill="none" stroke="#ffffff" stroke-width="1" opacity="0.3"/>
  
  <!-- Shield Icon -->
  <g transform="translate(180, 80)" fill="#ffffff" opacity="0.9">
    <path d="M20 2L20 2C20 2 20 6 20 10C20 18 16 24 10 26C4 24 0 18 0 10C0 6 0 2 0 2L20 2Z" stroke="#ffffff" stroke-width="1"/>
    <circle cx="10" cy="12" r="3" fill="#fbbf24"/>
  </g>
  
  <!-- Title -->
  <text x="200" y="160" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="32" font-weight="bold">THE Z_H_9</text>
  
  <!-- Subtitle Line -->
  <line x1="120" y1="180" x2="280" y2="180" stroke="#ffffff" stroke-width="2" opacity="0.5"/>
  
  <!-- Subtitle -->
  <text x="200" y="210" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="18" font-weight="normal">Zero to Hero in 9 Months</text>
  
  <!-- Main Title -->
  <text x="200" y="250" text-anchor="middle" fill="#fbbf24" font-family="Arial, sans-serif" font-size="20" font-weight="bold">The CyberWolf</text>
  <text x="200" y="275" text-anchor="middle" fill="#fbbf24" font-family="Arial, sans-serif" font-size="16" font-weight="normal">Cybersecurity Blueprint</text>
  
  <!-- Features List -->
  <g transform="translate(50, 320)" fill="#ffffff" font-family="Arial, sans-serif" font-size="14">
    <text x="0" y="0" font-weight="bold">✓ Comprehensive Roadmap</text>
    <text x="0" y="25" font-weight="bold">✓ Practical & Tool-Focused</text>
    <text x="0" y="50" font-weight="bold">✓ Real-World Scenarios</text>
    <text x="0" y="75" font-weight="bold">✓ Complete Domain Coverage</text>
  </g>
  
  <!-- Tools Icons -->
  <g transform="translate(60, 420)" fill="#10b981" opacity="0.7">
    <rect x="0" y="0" width="15" height="15" rx="2"/>
    <rect x="25" y="0" width="15" height="15" rx="2"/>
    <rect x="50" y="0" width="15" height="15" rx="2"/>
    <rect x="75" y="0" width="15" height="15" rx="2"/>
    <rect x="100" y="0" width="15" height="15" rx="2"/>
  </g>
  
  <!-- Tools Text -->
  <text x="200" y="460" text-anchor="middle" fill="#10b981" font-family="Arial, sans-serif" font-size="12">Kali Linux • Metasploit • Wireshark • Burp Suite • More</text>
  
  <!-- Bottom Section -->
  <rect x="50" y="490" width="300" height="60" rx="8" fill="#000000" opacity="0.3"/>
  <text x="200" y="515" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="16" font-weight="bold">Created by Cyber Wolf Team</text>
  <text x="200" y="535" text-anchor="middle" fill="#10b981" font-family="Arial, sans-serif" font-size="14" font-weight="bold">100% FREE DOWNLOAD</text>
  
  <!-- Free Badge -->
  <circle cx="320" cy="100" r="25" fill="#10b981"/>
  <text x="320" y="107" text-anchor="middle" fill="#ffffff" font-family="Arial, sans-serif" font-size="12" font-weight="bold">FREE</text>
  
  <!-- Decorative Elements -->
  <g transform="translate(50, 50)" fill="#ffffff" opacity="0.1">
    <circle cx="0" cy="0" r="3"/>
    <circle cx="280" cy="0" r="3"/>
    <circle cx="0" cy="480" r="3"/>
    <circle cx="280" cy="480" r="3"/>
  </g>
</svg>

import { motion } from "framer-motion";
import { ReactNode } from "react";

interface FeatureTabProps {
  icon: ReactNode;
  title: string;
  description: string;
  isActive: boolean;
}

export const FeatureTab = ({ icon, title, description, isActive }: FeatureTabProps) => {
  return (
    <div
      className={`
        w-full flex items-center gap-3 sm:gap-4 p-3 sm:p-4 md:p-5 rounded-lg md:rounded-xl
        transition-all duration-300 relative cursor-pointer
        ${isActive
          ? 'glass shadow-lg shadow-primary/10 border-l-4 border-primary'
          : 'hover:glass-hover hover:border-l-4 hover:border-primary/50'
        }
      `}
    >
      {isActive && (
        <motion.div
          layoutId="activeTab"
          className="absolute left-0 top-0 w-1 h-full bg-primary rounded-l-lg md:rounded-l-xl"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.2 }}
        />
      )}
      <div className="flex items-center gap-3 sm:gap-4 min-w-0 flex-1">
        <div className={`flex-shrink-0 ${isActive ? 'text-primary' : 'text-muted-foreground'}`}>
          {icon}
        </div>
        <div className="text-left min-w-0 flex-1">
          <h3 className={`font-semibold text-sm sm:text-base md:text-lg leading-tight ${isActive ? 'text-primary' : 'text-white'}`}>
            {title}
          </h3>
          <p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 mt-1 leading-relaxed">
            {description}
          </p>
        </div>
      </div>
    </div>
  );
};
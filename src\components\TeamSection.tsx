import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Github, Linkedin, Twitter } from "lucide-react";

const TeamSection = () => {
  const teamMembers = [
    {
      name: "Pugazhmani.K",
      role: "🛡️ Lead Cybersecurity Instructor",
      specialization: "Penetration Testing & Ethical Hacking",
      image: "https://cyberwolf-technology.web.app/cyberwolf_staffs/pugazhmani.jpg",
      bio: "2+ years defending Fortune 5 companies. Advanced persistent threats and zero-day exploits.",
      skills: ["Penetration Testing", "Network Security", "Malware Analysis"],
      social: {
        github: "#",
        linkedin: "#",
       
      }
    },
    {
      name: "Tamilselvan.S",
      role: "🏆 CTF Competition Expert",
      specialization: "Capture The Flag & Cryptography",
      image: "/images/WhatsApp Image 2025-06-24 at 7.52.27 PM.jpeg",
      bio: "Innovative technology professional specializing in Ethical Hacking, Application Development, and Cybersecurity. A prolific creator with a passion for developing secure and cutting-edge solutions that push the boundaries of technology..",
      skills: ["CTF Competitions", "Cryptography", "Reverse Engineering", "Binary Exploitation", "Steganography","Hardware"],
      social: {
        github: "https://github.com/Tamilselvan-S-Cyber-Security",
        linkedin: "https://www.linkedin.com/in/tamil-selvan-383618304/?utm_source=share&utm_campaign=share_via&utm_content=profile&utm_medium=android_app",
       
      }
    },
    {
      name: "Anbu Sivam B",
      role: "💻 Senior Electrical and Electronics",
      specialization: "Hardware & Development",
      image: "https://ctsolutionss.info/images/anbu.jpg",
      bio: "Won price among 100+ teams in a state-level hackathon by building a product for agri Project.",
      skills: ["Hardware Coding", "DevSecOps", "Code Review"],
      social: {
        github: "#",
        linkedin: "#",
       
      }
    }
    
    
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-gray-900 via-black to-gray-900 relative overflow-hidden">
      {/* Simplified Background Effects */}
      <div className="absolute inset-0 bg-gradient-to-r from-red-500/3 via-transparent to-blue-500/3"></div>

      <div className="container px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-20"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-block mb-6"
          >
            <div className="glass rounded-full px-6 py-2 border border-white/20">
              <span className="text-sm font-medium text-blue-400">
                🎯 Expert Instructors
              </span>
            </div>
          </motion.div>

          <h2 className="text-5xl md:text-7xl font-bold mb-8 relative">
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent">
              Meet Our
            </span>
            <br />
            <span className="bg-gradient-to-r from-red-400 via-red-500 to-red-600 bg-clip-text text-transparent">
              Expert Team
            </span>
          </h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed"
          >
            Learn from industry professionals with years of real-world cybersecurity experience.
            <br />
            <span className="text-blue-400 font-semibold">Our experts bring cutting-edge knowledge directly to you.</span>
          </motion.p>

          {/* Stats */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="flex flex-wrap justify-center gap-8 mt-12"
          >
            <div className="glass rounded-lg px-6 py-4 border border-white/10">
              <div className="text-2xl font-bold text-red-400">4+</div>
              <div className="text-sm text-gray-400">Years Combined Experience</div>
            </div>
            <div className="glass rounded-lg px-6 py-4 border border-white/10">
              <div className="text-2xl font-bold text-blue-400">100+</div>
              <div className="text-sm text-gray-400">Students Trained</div>
            </div>
            <div className="glass rounded-lg px-6 py-4 border border-white/10">
              <div className="text-2xl font-bold text-green-400">100%</div>
              <div className="text-sm text-gray-400">Industry Certified</div>
            </div>
          </motion.div>
        </motion.div>

        {/* Scrolling Team Members Container */}
        <div className="relative flex overflow-hidden py-8" style={{ transform: 'translateZ(0)' }}>
          <div className="animate-marquee-reverse flex min-w-full shrink-0 items-stretch gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={`${index}-1`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="w-[350px] shrink-0"
              >
                <Card className="glass-enhanced hover:border-red-400/50 transition-all duration-300 h-full shadow-lg hover:shadow-red-500/15 glow-red animate-float group">
                  <CardContent className="p-6 relative">

                    {/* Profile Image */}
                    <div className="relative mb-6">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-r from-red-500 to-blue-500 rounded-full p-1 shadow-lg">
                        <div className="w-full h-full bg-gray-900/90 rounded-full flex items-center justify-center border border-white/20 overflow-hidden">
                          <img
                            src={member.image && member.image.trim() !== "" ? member.image : "/placeholder.svg"}
                            alt={member.name + " profile"}
                            className="w-full h-full object-cover rounded-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Member Info */}
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold mb-2 text-white">
                        {member.name}
                      </h3>
                      <p className="text-red-400 font-semibold mb-2">{member.role}</p>
                      <p className="text-sm text-blue-300 mb-3 font-medium">{member.specialization}</p>
                      <p className="text-xs text-gray-300 leading-relaxed">{member.bio}</p>
                    </div>

                    {/* Skills */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1 justify-center">
                        {member.skills.slice(0, 4).map((skill, skillIndex) => (
                          <Badge
                            key={skillIndex}
                            variant="secondary"
                            className="bg-red-500/20 text-red-300 border-red-500/30 text-xs"
                          >
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Social Links */}
                    <div className="flex justify-center gap-4">
                      <a
                        href={member.social.github}
                        className="p-2 rounded-full bg-white/10 text-gray-400 hover:text-white transition-colors"
                        aria-label="GitHub"
                      >
                        <Github className="w-4 h-4" />
                      </a>
                      <a
                        href={member.social.linkedin}
                        className="p-2 rounded-full bg-white/10 text-gray-400 hover:text-blue-400 transition-colors"
                        aria-label="LinkedIn"
                      >
                        <Linkedin className="w-4 h-4" />
                      </a>
                      
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
          <div className="animate-marquee-reverse flex min-w-full shrink-0 items-stretch gap-8">
            {teamMembers.map((member, index) => (
              <motion.div
                key={`${index}-2`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
                className="w-[350px] shrink-0"
              >
                <Card className="glass-enhanced hover:border-red-400/50 transition-all duration-300 h-full shadow-lg hover:shadow-red-500/15 glow-red animate-float-delayed group">
                  <CardContent className="p-6 relative">
                    {/* Profile Image */}
                    <div className="relative mb-6">
                      <div className="w-24 h-24 mx-auto bg-gradient-to-r from-red-500 to-blue-500 rounded-full p-1 shadow-lg">
                        <div className="w-full h-full bg-gray-900/90 rounded-full flex items-center justify-center border border-white/20 overflow-hidden">
                          <img
                            src={member.image && member.image.trim() !== "" ? member.image : "/placeholder.svg"}
                            alt={member.name + " profile"}
                            className="w-full h-full object-cover rounded-full"
                          />
                        </div>
                      </div>
                    </div>

                    {/* Member Info */}
                    <div className="text-center mb-4">
                      <h3 className="text-xl font-bold mb-2 text-white">
                        {member.name}
                      </h3>
                      <p className="text-red-400 font-semibold mb-2">{member.role}</p>
                      <p className="text-sm text-blue-300 mb-3 font-medium">{member.specialization}</p>
                      <p className="text-xs text-gray-300 leading-relaxed">{member.bio}</p>
                    </div>

                    {/* Skills */}
                    <div className="mb-4">
                      <div className="flex flex-wrap gap-1 justify-center">
                        {member.skills.slice(0, 4).map((skill, skillIndex) => (
                          <Badge
                            key={skillIndex}
                            variant="secondary"
                            className="bg-red-500/20 text-red-300 border-red-500/30 text-xs"
                          >
                            {skill}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Social Links */}
                    <div className="flex justify-center gap-4">
                      <a
                        href={member.social.github}
                        className="p-2 rounded-full bg-white/10 text-gray-400 hover:text-white transition-colors"
                        aria-label="GitHub"
                      >
                        <Github className="w-4 h-4" />
                      </a>
                      <a
                        href={member.social.linkedin}
                        className="p-2 rounded-full bg-white/10 text-gray-400 hover:text-blue-400 transition-colors"
                        aria-label="LinkedIn"
                      >
                        <Linkedin className="w-4 h-4" />
                      </a>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  );
};

export default TeamSection;

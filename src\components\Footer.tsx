import { Github, Twitter, Mail, Phone, MapPin, Shield } from "lucide-react";
import { But<PERSON> } from "./ui/button";

const Footer = () => {
  
  return (
    <footer className="w-full py-12 mt-20">
      <div className="container px-4">
        <div className="glass glass-hover rounded-xl p-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
            <div className="space-y-4">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-red-400" />
                <h3 className="font-medium text-lg">Cyber Wolf</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                Empowering the next generation of cybersecurity professionals through hands-on training and expert guidance.
              </p>
              <div className="flex space-x-4">
                <Button variant="ghost" size="icon">
                  <Twitter className="w-4 h-4" />
                </Button>
                <Button variant="ghost" size="icon">
                  <Github className="w-4 h-4" />
                </Button>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Workshop</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#workshop-content" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                    Curriculum
                  </a>
                </li>
                <li>
                  <a href="#team" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                    Instructors
                  </a>
                </li>
                <li>
                  <a href="#registration" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                    Registration
                  </a>
                </li>
              </ul>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">College Info</h4>
              <div className="space-y-3">
                <div className="flex items-start gap-2">
                  <MapPin className="w-4 h-4 text-blue-400 mt-0.5" />
                  <div>
                    <p className="text-sm text-white font-medium">SKP Engineering College</p>
                    <p className="text-xs text-muted-foreground">Tiruvannamalai - 606 611</p>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4 text-green-400" />
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-yellow-400" />
                  <p className="text-sm text-muted-foreground">6374344424</p>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4 text-yellow-400" />
                  <p className="text-sm text-muted-foreground">9790155280</p>
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <h4 className="font-medium">Partners</h4>
              <ul className="space-y-2">
                <li>
                  <p className="text-sm text-red-400 font-medium">CT Tech Solutions</p>
                </li>
                <li>
                  <p className="text-sm text-blue-400 font-medium">Cyber Wolf Team</p>
                </li>
                <li>
                  <a href="#" className="text-sm text-muted-foreground hover:text-primary transition-colors">
                    Partnership Opportunities
                  </a>
                </li>
              </ul>
            </div>
          </div>

          {/* Certificate Section */}
          

          <div className="mt-8 pt-8 border-t border-white/10">
            <div className="text-center space-y-2">
              <p className="text-sm text-muted-foreground">
                © {new Date().getFullYear()} SKP Engineering College. All rights reserved.
              </p>
              <p className="text-xs text-muted-foreground">
                In association with CT Tech Solutions & Cyber Wolf
              </p>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
import { motion } from "framer-motion";
import { ArrowRight, Shield, Users, Code, Calendar, BookOpen, Download, Star, CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Link } from "react-router-dom";
import Navigation from "@/components/Navigation";
import WorkshopContent from "@/components/WorkshopContent";
import TeamSection from "@/components/TeamSection";
import { FeaturesSection } from "@/components/features/FeaturesSection";
import TestimonialsSection from "@/components/TestimonialsSection";
import Footer from "@/components/Footer";
import { TextGenerateEffect } from "@/components/ui/text-generate-effect";

const Index = () => {
  return (
    <div className="min-h-screen bg-black text-foreground">
      <Navigation />
      
      {/* Hero Section */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        className="relative container px-4 pt-40 pb-20"
      >
        {/* Background */}
        <div
          className="absolute inset-0 -z-10 bg-[#0A0A0A]"
        />

        {/* College Branding with Background Image */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.1 }}
          className="relative text-center mb-10"
        >
          {/* Background Image Container */}
          <div
            className="absolute inset-0 -mx-4 -my-8 rounded-2xl overflow-hidden"
            style={{
              backgroundImage: 'url("/images/0dbe1b75-2c74-4ff8-ba55-4be4d74abe72.png")',
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              backgroundRepeat: 'no-repeat'
            }}
          >
            {/* Overlay for better text readability */}
            <div className="absolute inset-0 bg-black/60"></div>
            <div className="absolute inset-0 bg-gradient-to-b from-black/40 via-transparent to-black/60"></div>
          </div>

          {/* Content */}
          <div className="relative z-10 py-12 px-6">
            <h3 className="college-title text-2xl md:text-4xl mb-4 drop-shadow-2xl tracking-wider">
              SKP ENGINEERING COLLEGE
            </h3>
            <p className="college-location text-lg md:text-xl mb-6 tracking-wide drop-shadow-lg">
              TIRUVANNAMALAI - 606 611
            </p>
            <div className="flex items-center justify-center mb-4">
              <div className="h-px bg-gradient-to-r from-transparent via-green-500 to-transparent w-32"></div>
              <p className="association-text mx-6 tracking-widest drop-shadow-lg">
                IN ASSOCIATION WITH
              </p>
              <div className="h-px bg-gradient-to-r from-transparent via-green-500 to-transparent w-32"></div>
            </div>
            <p className="partner-names text-xl md:text-2xl drop-shadow-2xl tracking-wide">
              CT TECH SOLUTIONS & CYBER WOLF
            </p>

            {/* Workshop Details */}
            <div className="mt-8 space-y-4">
              <div className="bg-black/40 backdrop-blur-sm rounded-xl p-6 border border-green-500/30">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                  <div className="space-y-2">
                    <h4 className="text-green-400 font-bold text-lg">Duration</h4>
                    <p className="text-white font-mono text-xl">2 Days Workshop</p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-green-400 font-bold text-lg">Registration Fee</h4>
                    <p className="text-white font-mono text-2xl font-bold">₹250 Only</p>
                  </div>
                  <div className="space-y-2">
                    <h4 className="text-green-400 font-bold text-lg">Contact</h4>
                    <div className="space-y-1">
                      <p className="text-white font-mono text-sm"><EMAIL></p>
                      <p className="text-white font-mono text-sm">📞 6374344424</p>
                      <p className="text-white font-mono text-sm">📞 9790155280</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
          className="inline-block mb-4 px-4 py-1.5 rounded-full glass mx-auto block"
        >
          <span className="text-sm font-medium">
            <Shield className="w-4 h-4 inline-block mr-2" />
            Two-Day Cybersecurity Workshop
          </span>
        </motion.div>
        <motion.div
  initial={{ opacity: 0, x: 20 }}
  animate={{ opacity: 1, x: 0 }}
  transition={{ duration: 0.6, delay: 0.9 }}
  className="flex justify-center"
>
  <img
    src="images/3d-rendering-red-flag-isolated-white-background_1121794-716-Photoroom.png"
    alt="3D Character Working on Computer - Cybersecurity Learning"
    className="w-32 h-auto object-contain rounded-2xl"
    loading="lazy"
  />
</motion.div>


        <div className="max-w-4xl relative z-10 text-center mx-auto">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 tracking-tight">
            <span className="text-gray-200">
              <TextGenerateEffect words="CTF Workshop" />
            </span>
            <br />
            <h1>&</h1>
            <span className="text-white font-medium">
              <TextGenerateEffect words="Capture The Flag Competition" />
            </span>
            
          </h1>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="text-lg md:text-xl text-gray-200 mb-8 max-w-3xl mx-auto"
          >
            Join the ultimate cybersecurity challenge. Test your skills in web exploitation, cryptography, reverse engineering, and more!
            Learn cybersecurity fundamentals, CTF playing methods, and master multiple programming languages in this intensive workshop.
          </motion.p>

          {/* Workshop Highlights */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5 }}
            className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8 max-w-3xl mx-auto"
          >
            <div className="flex items-center justify-center gap-2 text-green-400">
              <Code className="w-5 h-5" />
              <span>Multiple Programming Languages</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-blue-400">
              <Shield className="w-5 h-5" />
              <span>Cybersecurity Fundamentals</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-purple-400">
              <Users className="w-5 h-5" />
              <span>Hands-on CTF Training</span>
            </div>
          </motion.div>

          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="flex flex-col sm:flex-row gap-4 items-center justify-center"
          >
            <Link to="/register">
              <Button size="lg" className="button-gradient">
                <Calendar className="mr-2 w-4 h-4" />
                Register Now
              </Button>
            </Link>
           
            
           
          </motion.div>
        </div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="relative mx-auto max-w-6xl mt-20"
        >
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* Workshop Schedule */}
            <div className="glass rounded-xl overflow-hidden bg-gradient-to-r from-red-900/20 to-blue-900/20 p-8">
              <div className="text-center">
                <h3 className="text-2xl font-bold mb-6 text-white">Workshop Schedule</h3>
                <div className="space-y-6 text-left">
                  <div className="bg-black/40 p-6 rounded-lg border border-blue-500/30">
                    <h4 className="text-lg font-semibold text-blue-400 mb-3 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Day 1: Fundamentals
                    </h4>
                    <ul className="text-gray-300 space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                        Cybersecurity Basics
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                        Network Security
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                        Web Application Security
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-blue-400 rounded-full"></span>
                        Introduction to CTF
                      </li>
                    </ul>
                  </div>
                  <div className="bg-black/40 p-6 rounded-lg border border-red-500/30">
                    <h4 className="text-lg font-semibold text-red-400 mb-3 flex items-center gap-2">
                      <Calendar className="w-5 h-5" />
                      Day 2: Practical
                    </h4>
                    <ul className="text-gray-300 space-y-2">
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                        Hands-on CTF Challenges
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                        Programming for Security
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                        Tool Usage & Techniques
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-2 h-2 bg-red-400 rounded-full"></span>
                        Team Competition
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            {/* 3D Image */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.9 }}
              className="flex justify-center"
            >
              <img
                src="images/man-sits-laptop-with-word-web-screen_1181564-2976-Photoroom.png"
                alt="3D Character Working on Computer - Cybersecurity Learning"
                className="w-full h-auto object-contain rounded-2xl max-w-lg"
                loading="lazy"
              />
            </motion.div>
          </div>
        </motion.div>
      </motion.section>

      {/* Workshop Content Section */}
      <div id="workshop-content">
        <WorkshopContent />
      </div>

      {/* Team Section */}
      <div id="team">
        <TeamSection />
      </div>

      {/* Features Section */}
      <div id="features" className="bg-black">
        <FeaturesSection />
      </div>

      {/* Free Cybersecurity Roadmap Book Section */}
      <section className="py-20 bg-gradient-to-b from-black via-gray-900 to-black relative overflow-hidden">
        {/* Background Effects */}
        <div className="absolute inset-0">
          <div className="absolute top-20 left-10 w-40 h-40 bg-green-500/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
        </div>

        <div className="container px-4 relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-16"
          >
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              whileInView={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="inline-block mb-6"
            >
              <div className="glass rounded-full px-6 py-2 border border-white/20">
                <span className="text-sm font-medium text-green-400">
                  🎁 Limited Time Offer
                </span>
              </div>
            </motion.div>

            <h2 className="text-4xl md:text-6xl font-bold mb-6">
              <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent">
                Free Cybersecurity
              </span>
              <br />
              <span className="bg-gradient-to-r from-green-400 via-green-500 to-green-600 bg-clip-text text-transparent">
                Roadmap Book
              </span>
            </h2>

            <motion.p
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              className="text-xl text-gray-300 max-w-3xl mx-auto mb-8"
            >
              Get your hands on our comprehensive cybersecurity learning guide -
              <span className="text-green-400 font-semibold"> absolutely free!</span>
            </motion.p>
          </motion.div>

          <div className="grid lg:grid-cols-2 gap-12 items-center max-w-6xl mx-auto">
            {/* Book Preview */}
            <motion.div
              initial={{ opacity: 0, x: -50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="relative"
            >
              <div className="relative max-w-md mx-auto">
                {/* Book Cover Image */}
                <motion.div
                  whileHover={{ scale: 1.05, rotateY: 5 }}
                  transition={{ duration: 0.3 }}
                  className="relative transform rotate-2 hover:rotate-0 transition-transform duration-500"
                >
                  {/* Book Shadow */}
                  <div className="absolute inset-0 bg-black/50 rounded-2xl blur-xl transform translate-x-4 translate-y-4"></div>

                  {/* Actual Book Image */}
                  <div className="relative bg-gradient-to-br from-gray-900 to-black rounded-2xl p-3 shadow-2xl border border-white/20">
                    <img
                      src="/images/Screenshot 2025-08-03 212556-Photoroom.png"
                      alt="THE Z_H_9 - Zero to Hero in 9 Months Cybersecurity Book"
                      className="w-full h-auto rounded-xl shadow-lg object-contain"
                    />

                    {/* Overlay for better contrast */}
                    <div className="absolute inset-3 rounded-xl bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"></div>
                  </div>
                </motion.div>

                {/* Price Tag */}
                <motion.div
                  animate={{ rotate: [0, 5, -5, 0] }}
                  transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                  className="absolute -top-6 -right-6 bg-gradient-to-r from-red-500 to-red-600 text-white px-6 py-3 rounded-full font-bold text-lg shadow-xl z-20"
                >
                  <span className="line-through text-red-200 text-sm">₹199</span>
                  <span className="ml-2 text-white text-xl">FREE!</span>
                </motion.div>

                {/* Floating Elements */}
                <motion.div
                  animate={{
                    y: [0, -10, 0],
                    rotate: [0, 5, -5, 0]
                  }}
                  transition={{
                    duration: 4,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                  className="absolute -top-8 -left-8 text-green-400/60"
                >
                  <Star className="w-8 h-8" />
                </motion.div>

                <motion.div
                  animate={{
                    y: [0, 8, 0],
                    rotate: [0, -5, 5, 0]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "easeInOut",
                    delay: 1
                  }}
                  className="absolute -bottom-6 -right-8 text-blue-400/60"
                >
                  <Shield className="w-6 h-6" />
                </motion.div>

                {/* Glow Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-green-500/20 to-blue-500/20 rounded-2xl blur-2xl animate-pulse"></div>
              </div>
            </motion.div>

            {/* Book Details */}
            <motion.div
              initial={{ opacity: 0, x: 50 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-6"
            >
              <div>
                <h3 className="text-3xl font-bold text-white mb-4">
                  The Complete Cybersecurity Learning Path
                </h3>
                <p className="text-gray-300 text-lg leading-relaxed">
                  Master cybersecurity from zero to hero with our comprehensive 9-month roadmap.
                  Created by the Cyber Wolf Team with real-world expertise.
                </p>
              </div>

              {/* Key Features */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold mb-1">Comprehensive Roadmap</h4>
                    <p className="text-gray-400 text-sm">Structured 9-month learning path covering basic to advanced cybersecurity skills</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold mb-1">Practical & Tool-Focused</h4>
                    <p className="text-gray-400 text-sm">Deep dive into Kali Linux, Metasploit, Wireshark, Burp Suite, MobSF, Frida & more</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold mb-1">Real-World Scenarios</h4>
                    <p className="text-gray-400 text-sm">Learn from case studies, CTF challenges, and ethical hacking techniques</p>
                  </div>
                </div>

                <div className="flex items-start gap-3">
                  <CheckCircle className="w-6 h-6 text-green-400 mt-1 flex-shrink-0" />
                  <div>
                    <h4 className="text-white font-semibold mb-1">Complete Domain Coverage</h4>
                    <p className="text-gray-400 text-sm">Web/Mobile Security, Cloud Security, Network Security, Malware Analysis & more</p>
                  </div>
                </div>
              </div>

              {/* Download Button */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.6 }}
                className="pt-6"
              >
                <a
                  href="/images/Screenshot 2025-08-03 212556-Photoroom.png"
                  download="CyberWolf-Z_H_9-Cybersecurity-Roadmap-Book.png"
                  className="inline-block"
                >
                  <Button
                    size="lg"
                    className="bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-semibold px-8 py-4 rounded-xl shadow-lg hover:shadow-green-500/25 transition-all duration-300 group"
                  >
                    <Download className="mr-3 w-5 h-5 group-hover:scale-110 transition-transform" />
                    Download Free Book
                    <Star className="ml-3 w-5 h-5 text-yellow-300 group-hover:scale-110 transition-transform" />
                  </Button>
                </a>
                <p className="text-gray-500 text-sm mt-2">
                  No registration required • Instant download • 100% Free
                </p>
              </motion.div>
            </motion.div>
          </div>

          {/* Workshop Integration */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="mt-16 text-center"
          >
            <div className="glass-enhanced border-green-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
              <h3 className="text-2xl font-bold text-white mb-4">
                Ready to Start Your Journey?
              </h3>
              <p className="text-gray-300 mb-6">
                Download the free roadmap book and join our hands-on cybersecurity workshop to accelerate your learning!
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link to="/register">
                  <Button size="lg" className="button-gradient">
                    <Calendar className="mr-2 w-4 h-4" />
                    Join Workshop
                  </Button>
                </Link>
                <a
                  href="/images/Screenshot 2025-08-03 212556-Photoroom.png"
                  download="CyberWolf-Z_H_9-Cybersecurity-Roadmap-Book.png"
                >
                  <Button
                    size="lg"
                    variant="outline"
                    className="border-green-400 text-green-400 hover:bg-green-400 hover:text-white"
                  >
                    <BookOpen className="mr-2 w-4 h-4" />
                    Get Free Book
                  </Button>
                </a>
              </div>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Testimonials Section */}
      <div className="bg-black">
        <TestimonialsSection />
      </div>

      {/* CTA Section */}
      <section className="container px-4 py-20 relative bg-black">
        <div
          className="absolute inset-0 opacity-20 bg-gradient-to-r from-red-900/30 to-blue-900/30"
        />
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-[#0A0A0A]/80 backdrop-blur-lg border border-red-400/20 rounded-2xl p-8 md:p-12 text-center relative z-10"
        >
          <div className="border-t border-white/10 pt-8 mb-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-4">
                Workshop <span className="text-green-400">Certificate</span>
              </h3>
              <p className="text-gray-300 max-w-2xl mx-auto">
                Upon successful completion of the 2-day workshop, participants will receive an official certificate
                from SKP Engineering College in association with Cyber Wolf.
              </p>
            </div>

            <div className="flex justify-center">
              <div className="relative max-w-4xl w-full">
                <img
                  src="https://nature-basket-online.web.app/public/images/certificate.png"
                  alt="Workshop Certificate Sample"
                  className="w-full h-auto object-contain rounded-2xl shadow-2xl"
                  loading="lazy"
                />

                {/* Certificate overlay info */}
                
              </div>
            </div>
          </div>
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            Ready to Master <span className="text-red-400">Cybersecurity</span>?
          </h2>
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto">
            Don't miss this opportunity to learn from industry experts and kickstart your cybersecurity journey.
            Limited seats available - register now!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link to="/register">
              <Button size="lg" className="button-gradient">
                <Calendar className="mr-2 w-4 h-4" />
                Register Now
              </Button>
            </Link>
            <a
          href="/images/brochure.png"
          download
          className="inline-flex flex-col items-center px-4 py-2 text-sm border rounded-lg border-red-400 text-red-400 hover:bg-red-400 hover:text-white transition-colors">
         <span>Download CTF Brochure</span>
         
          </a>

          </div>
        </motion.div>
      </section>

      {/* Footer */}
      <div className="bg-black">
        <Footer />
      </div>
    </div>
  );
};

export default Index;

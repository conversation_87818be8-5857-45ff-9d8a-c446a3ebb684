import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Clock, Calendar } from "lucide-react";

interface ScheduleItem {
  time: string;
  session: string;
}

const ScheduleSection = () => {
  const day1Schedule: ScheduleItem[] = [
    { time: "9:30 AM", session: "Participant Reporting & Setup" },
    { time: "9:40 AM", session: "Inauguration & Introduction to CTF" },
    { time: "10:20 AM", session: "CTF Methodologies & Working Models" },
    { time: "11:00 AM", session: "Purpose & Real-World Applications of CTFs" },
    { time: "11:30 AM", session: "Fundamentals of CTF Challenges" },
    { time: "12:00 PM", session: "Understanding Exploits, Databases & Vulnerability Scanning" },
    { time: "1:00 PM", session: "Lunch Break" },
    { time: "2:00 PM", session: "Introduction to Free CTF Tools & Model Building Techniques" },
    { time: "2:30 PM", session: "Server-Side Injections & XSS, CSRF Attacks Explained" },
    { time: "3:00 PM", session: "Intro to Hardware Hacking Concepts" },
    { time: "4:00 PM", session: "Wrap-up & Q/A Session" }
  ];

  const day2Schedule: ScheduleItem[] = [
    { time: "9:30 AM", session: "Welcome & Recap of Day 1" },
    { time: "10:00 AM", session: "Live CTF Challenge Launch" },
    { time: "11:00 AM", session: "CTF Problem Solving in Real-Time" },
    { time: "12:00 PM", session: "Open Discussion: Approach & Strategy" },
    { time: "1:00 PM", session: "Lunch Break" },
    { time: "2:00 PM", session: "Reverse Engineering Techniques" },
    { time: "2:30 PM", session: "Hardware Control via CTF Challenges" },
    { time: "3:00 PM", session: "Dictionary Attacks & Brute Forcing Explained" },
    { time: "3:30 PM", session: "Identifying Security Issues & Report Writing in CTFs" },
    { time: "4:00 PM", session: "Certificate Distribution & Closing Note" }
  ];

  return (
    <section className="py-20 bg-black relative overflow-hidden">
      {/* Edge Intersection Background Effect */}
      <div className="absolute inset-0">
        <div className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-blue-500/10 opacity-20"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,0,0,0.1),transparent_50%)]"></div>
        <div className="grid grid-cols-[repeat(20,1fr)] grid-rows-[repeat(20,1fr)] h-full w-full opacity-30">
          {Array.from({ length: 400 }).map((_, i) => (
            <div
              key={i}
              className="border-[0.5px] border-gray-500/20"
              style={{
                gridColumn: `${(i % 20) + 1} / span 1`,
                gridRow: `${Math.floor(i / 20) + 1} / span 1`,
              }}
            />
          ))}
        </div>
      </div>

      <div className="container px-4 relative z-10">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <div className="inline-block mb-4">
            <div className="flex items-center gap-2 bg-red-500/10 rounded-full px-4 py-2 border border-red-500/20">
              <Clock className="w-5 h-5 text-red-400" />
              <span className="text-red-400 font-medium">Daily Schedule</span>
            </div>
          </div>
          <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-white to-gray-300">
            Workshop Timeline
          </h2>
          <p className="text-gray-400 max-w-2xl mx-auto">
            Timing: 9:30 AM – 4:00 PM
          </p>
        </motion.div>

        <div className="grid md:grid-cols-2 gap-8">
          {/* Day 1 Schedule */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-gray-900/50 border-gray-800 hover:border-red-500/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-6">
                  <Calendar className="w-5 h-5 text-red-400" />
                  <h3 className="text-xl font-bold text-white">Day 1 – Introduction to CTF & Cybersecurity Fundamentals</h3>
                </div>
                <div className="space-y-4">
                  {day1Schedule.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex gap-4 group hover:bg-gray-800/50 p-2 rounded-lg transition-all duration-300"
                    >
                      <div className="w-20 text-red-400 font-mono group-hover:text-red-300 transition-colors">
                        {item.time}
                      </div>
                      <div className="flex-1 text-gray-300 group-hover:text-white transition-colors">
                        {item.session}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Day 2 Schedule */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
          >
            <Card className="bg-gray-900/50 border-gray-800 hover:border-blue-500/50 transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-6">
                  <Calendar className="w-5 h-5 text-blue-400" />
                  <h3 className="text-xl font-bold text-white">Day 2 – Live CTF, Reverse Engineering & Hardware Exploits</h3>
                </div>
                <div className="space-y-4">
                  {day2Schedule.map((item, index) => (
                    <motion.div
                      key={index}
                      initial={{ opacity: 0, y: 10 }}
                      whileInView={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.3, delay: index * 0.1 }}
                      className="flex gap-4 group hover:bg-gray-800/50 p-2 rounded-lg transition-all duration-300"
                    >
                      <div className="w-20 text-blue-400 font-mono group-hover:text-blue-300 transition-colors">
                        {item.time}
                      </div>
                      <div className="flex-1 text-gray-300 group-hover:text-white transition-colors">
                        {item.session}
                      </div>
                    </motion.div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default ScheduleSection; 
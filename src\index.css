@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Oswald:wght@200;300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Share+Tech+Mono&display=swap');
@import url('https://fonts.geist-ui.dev/font.css');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 4%;
    --foreground: 0 0% 100%;
    --card: 0 0% 4%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 4%;
    --popover-foreground: 0 0% 100%;
    --primary: 142 84% 58%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 9%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 9%;
    --muted-foreground: 0 0% 64%;
    --accent: 0 0% 9%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 12%;
    --input: 0 0% 12%;
    --ring: 142 84% 58%;
    --radius: 0.75rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.glass {
  @apply bg-white/5 backdrop-blur-lg;
  border: 1px solid transparent;
  background-clip: padding-box;
  position: relative;
}

.glass::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(225deg, rgba(255,255,255,0.2), rgba(255,255,255,0.05));
  -webkit-mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
  -webkit-mask-composite: xor;
  mask-composite: exclude;
  pointer-events: none;
}

.glass-hover {
  @apply transition-all duration-300 hover:bg-white/10;
}

.text-gradient {
  @apply bg-clip-text text-transparent bg-gradient-to-r from-primary to-[#22c55e];
}

.button-gradient {
  @apply bg-gradient-to-r from-primary to-[#22c55e] hover:opacity-90 transition-opacity rounded-full;
}

/* Custom Typography Classes */
.college-title {
  font-family: 'Share Tech Mono', monospace;
  font-weight: 400;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  color: white;
}

.college-location {
  font-family: 'Share Tech Mono', monospace;
  font-weight: 400;
  letter-spacing: 0.08em;
  color: white;
}

.association-text {
  font-family: 'Share Tech Mono', monospace;
  font-weight: 400;
  letter-spacing: 0.15em;
  font-size: 0.875rem;
  color: #10b981; /* green-500 */
}

.partner-names {
  font-family: 'Share Tech Mono', monospace;
  font-weight: 400;
  letter-spacing: 0.08em;
  color: #10b981; /* green-500 */
}

/* Marquee Animations */
@keyframes marquee {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-100%);
  }
}

@keyframes marquee-reverse {
  0% {
    transform: translate3d(-100%, 0, 0);
  }
  100% {
    transform: translate3d(0%, 0, 0);
  }
}

.animate-marquee {
  animation: marquee 25s linear infinite;
  will-change: transform;
}

.animate-marquee-reverse {
  animation: marquee-reverse 35s linear infinite;
  will-change: transform;
}

/* Enhanced Glass Effects - Optimized */
.glass-enhanced {
  @apply bg-white/5 backdrop-blur-lg border border-white/10;
  background: linear-gradient(135deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03));
  box-shadow: 0 4px 16px 0 rgba(31, 38, 135, 0.2);
  will-change: transform, box-shadow;
}

.glass-enhanced:hover {
  background: rgba(255, 255, 255, 0.08);
  box-shadow:
    0 8px 24px 0 rgba(31, 38, 135, 0.3),
    0 0 12px 0 rgba(239, 68, 68, 0.15);
  transform: translateY(-2px);
}

/* Optimized Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translate3d(0, 0px, 0);
  }
  50% {
    transform: translate3d(0, -6px, 0);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
  will-change: transform;
}

.animate-float-delayed {
  animation: float 8s ease-in-out infinite;
  animation-delay: 3s;
  will-change: transform;
}

/* Simplified Glow Effects */
.glow-red:hover {
  box-shadow: 0 0 15px rgba(239, 68, 68, 0.25);
}

.glow-blue:hover {
  box-shadow: 0 0 15px rgba(59, 130, 246, 0.25);
}

.glow-purple:hover {
  box-shadow: 0 0 15px rgba(147, 51, 234, 0.25);
}
import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, MapPin, Users, Mail, Phone, User, GraduationCap } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { EMAILJS_CONFIG } from "@/config/emailjs";
import emailjs from '@emailjs/browser';
import { useNavigate } from "react-router-dom";

const RegistrationForm = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    college: "",
    year: "",
    branch: "",
    experience: "",
    expectations: "",
    teamPreference: "",
    paymentProof: null as File | null,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Generate a unique registration ID: CY01 + 16 digits
  const generateRegistrationId = () => {
    const randomDigits = Array.from({ length: 16 }, () => Math.floor(Math.random() * 10)).join("");
    return `CY01${randomDigits}`;
  };

  const handleInputChange = (field: string, value: string | File | null) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    const registrationId = generateRegistrationId();

    try {
      // Prepare EmailJS params
      const templateParams: any = {
        from_name: formData.fullName,
        from_email: formData.email,
        phone: formData.phone,
        college: formData.college,
        year: formData.year,
        branch: formData.branch,
        experience: formData.experience,
        expectations: formData.expectations,
        registration_id: registrationId,
      };

      // Send email via EmailJS
      await emailjs.send(
        EMAILJS_CONFIG.SERVICE_ID,
        EMAILJS_CONFIG.TEMPLATE_ID,
        templateParams,
        EMAILJS_CONFIG.PUBLIC_KEY
      );

      toast({
        title: "Registration Successful!",
        description: `Your registration ID is ${registrationId}. Please check your email for further instructions.`,
      });

      // Redirect to success page with registration ID
      navigate(`/registration-success?id=${registrationId}`);

      // Reset form
      setFormData({
        fullName: "",
        email: "",
        phone: "",
        college: "",
        year: "",
        branch: "",
        experience: "",
        expectations: "",
        teamPreference: "",
        paymentProof: null,
      });
    } catch (error) {
      toast({
        title: "Registration Failed",
        description: "Something went wrong. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section className="py-20 bg-black">
      <div className="container px-4">
        <div className="max-w-6xl mx-auto">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h2 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-white">Register for </span>
              <span className="text-red-400">Workshop</span>
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Secure your spot in this exclusive cybersecurity workshop. Limited seats available!
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Workshop Details */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
            >
              <Card className="bg-gray-900/50 border-gray-700 h-full">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <Calendar className="w-5 h-5 text-red-400" />
                    Workshop Details
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Everything you need to know about the workshop
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="flex items-start gap-3">
                    <Calendar className="w-5 h-5 text-blue-400 mt-1" />
                    <div>
                      <h4 className="font-semibold text-white">Date & Duration</h4>
                      <p className="text-gray-300">2 Days Workshop</p>
                      <p className="text-sm text-gray-400">Dates to be announced soon</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Clock className="w-5 h-5 text-green-400 mt-1" />
                    <div>
                      <h4 className="font-semibold text-white">Timing</h4>
                      <p className="text-gray-300">9:00 AM - 5:00 PM</p>
                      <p className="text-sm text-gray-400">Includes lunch and refreshments</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <MapPin className="w-5 h-5 text-purple-400 mt-1" />
                    <div>
                      <h4 className="font-semibold text-white">Venue</h4>
                      <p className="text-gray-300">SKP Engineering College</p>
                      <p className="text-sm text-gray-400">Tiruvannamalai - 606 611</p>
                    </div>
                  </div>

                  <div className="flex items-start gap-3">
                    <Users className="w-5 h-5 text-yellow-400 mt-1" />
                    <div>
                      <h4 className="font-semibold text-white">Capacity</h4>
                      <p className="text-gray-300">Limited to 100 participants</p>
                      <p className="text-sm text-gray-400">First come, first served</p>
                    </div>
                  </div>

                  <div className="pt-4 border-t border-gray-700">
                    <div className="mb-4">
                      <h4 className="font-semibold text-white mb-2">Registration Fee:</h4>
                      <p className="text-2xl font-bold text-green-400">₹250 Only</p>
                    </div>

                    <div className="mb-4">
                      <h4 className="font-semibold text-white mb-2">Contact Information:</h4>
                      <div className="space-y-1 text-sm">
                        <p className="text-gray-300">📧 <EMAIL></p>
                        <p className="text-gray-300">📞 6374344424</p>
                        <p className="text-gray-300">📞 9790155280</p>
                      </div>
                    </div>

                    <h4 className="font-semibold text-white mb-3">What's Included:</h4>
                    <div className="flex flex-wrap gap-2">
                      <Badge variant="secondary" className="bg-red-500/20 text-red-300">Workshop Materials</Badge>
                      <Badge variant="secondary" className="bg-blue-500/20 text-blue-300">Certificate</Badge>
                      <Badge variant="secondary" className="bg-green-500/20 text-green-300">Lunch & Snacks</Badge>
                      <Badge variant="secondary" className="bg-purple-500/20 text-purple-300">CTF Access</Badge>
                    </div>

                    <h4 className="font-semibold text-white mb-3">How to Complete Registration:</h4>
                    <div className="text-gray-300 text-sm mb-2">
                      1. Fill the form and submit.<br />
                      2. You will receive a unique Registration ID.<br />
                      3. Send your payment proof (screenshot/receipt) to <span className="text-green-400"><EMAIL></span> with your Registration ID.<br />
                      4. Our team will verify and confirm your spot.<br />
                      5. Join the workshop on the announced date!
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Registration Form */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5, delay: 0.4 }}
            >
              <Card className="bg-gray-900/50 border-gray-700">
                <CardHeader>
                  <CardTitle className="text-white flex items-center gap-2">
                    <User className="w-5 h-5 text-red-400" />
                    Registration Form
                  </CardTitle>
                  <CardDescription className="text-gray-300">
                    Fill in your details to secure your spot
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="fullName" className="text-white">Full Name *</Label>
                        <Input
                          id="fullName"
                          value={formData.fullName}
                          onChange={(e) => handleInputChange("fullName", e.target.value)}
                          className="bg-black/50 border-gray-600 text-white"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="email" className="text-white">Email *</Label>
                        <Input
                          id="email"
                          type="email"
                          value={formData.email}
                          onChange={(e) => handleInputChange("email", e.target.value)}
                          className="bg-black/50 border-gray-600 text-white"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="phone" className="text-white">Phone Number *</Label>
                        <Input
                          id="phone"
                          value={formData.phone}
                          onChange={(e) => handleInputChange("phone", e.target.value)}
                          className="bg-black/50 border-gray-600 text-white"
                          required
                        />
                      </div>
                      <div>
                        <Label htmlFor="college" className="text-white">College/Institution *</Label>
                        <Input
                          id="college"
                          value={formData.college}
                          onChange={(e) => handleInputChange("college", e.target.value)}
                          className="bg-black/50 border-gray-600 text-white"
                          required
                        />
                      </div>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="year" className="text-white">Year of Study</Label>
                        <Select onValueChange={(value) => handleInputChange("year", value)}>
                          <SelectTrigger className="bg-black/50 border-gray-600 text-white">
                            <SelectValue placeholder="Select year" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1st">1st Year</SelectItem>
                            <SelectItem value="2nd">2nd Year</SelectItem>
                            <SelectItem value="3rd">3rd Year</SelectItem>
                            <SelectItem value="4th">4th Year</SelectItem>
                            <SelectItem value="graduate">Graduate</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label htmlFor="branch" className="text-white">Branch/Department</Label>
                        <Input
                          id="branch"
                          value={formData.branch}
                          onChange={(e) => handleInputChange("branch", e.target.value)}
                          className="bg-black/50 border-gray-600 text-white"
                          placeholder="e.g., Computer Science"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="experience" className="text-white">Cybersecurity Experience</Label>
                      <Select onValueChange={(value) => handleInputChange("experience", value)}>
                        <SelectTrigger className="bg-black/50 border-gray-600 text-white">
                          <SelectValue placeholder="Select experience level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="beginner">Complete Beginner</SelectItem>
                          <SelectItem value="basic">Basic Knowledge</SelectItem>
                          <SelectItem value="intermediate">Intermediate</SelectItem>
                          <SelectItem value="advanced">Advanced</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div>
                      <Label htmlFor="expectations" className="text-white">What do you hope to learn?</Label>
                      <Textarea
                        id="expectations"
                        value={formData.expectations}
                        onChange={(e) => handleInputChange("expectations", e.target.value)}
                        className="bg-black/50 border-gray-600 text-white"
                        placeholder="Tell us about your learning goals..."
                        rows={3}
                      />
                    </div>

                    <div>
                      <Label htmlFor="paymentProof" className="text-white">Upload Payment Proof (optional)</Label>
                      <Input
                        id="paymentProof"
                        type="file"
                        accept="image/*,application/pdf"
                        onChange={(e) => handleInputChange("paymentProof", e.target.files ? e.target.files[0] : null)}
                        className="bg-black/50 border-gray-600 text-white"
                      />
                      <p className="text-xs text-gray-400 mt-1">You can also email your payment proof later with your Registration ID.</p>
                    </div>

                    <Button 
                      type="submit" 
                      className="w-full button-gradient" 
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? "Registering..." : "Register Now"}
                    </Button>
                  </form>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default RegistrationForm;

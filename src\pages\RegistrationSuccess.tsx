import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card";
import { CheckCircle, Calendar, Mail, Phone, MapPin, Download, Home, QrCode } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { QRCodeCanvas } from "qrcode.react";
import { useEffect, useState } from "react";

const RegistrationSuccess = () => {
  const location = useLocation();
  const [registrationId, setRegistrationId] = useState("");
  const [timestamp, setTimestamp] = useState("");
  const [personName, setPersonName] = useState("");
  const [transactionId, setTransactionId] = useState("");

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const id = params.get("id") || "";
    const name = params.get("name") || "";
    setRegistrationId(id);
    setTimestamp(new Date().toLocaleString());
    setPersonName(name);
  }, [location.search]);

  // Download QR code as image
  const downloadQRCode = () => {
    const canvas = document.getElementById("registration-qr") as HTMLCanvasElement;
    if (canvas) {
      const url = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.href = url;
      link.download = `Registration_QR_${registrationId}.png`;
      link.click();
    }
  };

  // Helper for downloading QR by element ID
  function downloadQRCodeById(elementId: string, filename: string) {
    const canvas = document.getElementById(elementId) as HTMLCanvasElement;
    if (canvas) {
      const url = canvas.toDataURL("image/png");
      const link = document.createElement("a");
      link.href = url;
      link.download = filename;
      link.click();
    }
  }

  return (
    <div className="min-h-screen bg-black flex items-center justify-center py-20">
      <div className="container px-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.5 }}
          className="max-w-2xl mx-auto text-center"
        >
          {/* Success Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            className="mb-8"
          >
            <div className="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-12 h-12 text-white" />
            </div>
          </motion.div>

          {/* Registration ID and QR Code */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="mb-8"
          >
            <h2 className="text-2xl font-bold text-white mb-2">Your Registration ID</h2>
            <div className="text-2xl font-mono text-green-400 bg-green-900/20 px-6 py-3 rounded-lg inline-block mb-4">
              {registrationId}
              {personName && (
                <div className="text-base text-blue-300 mt-1">{personName}</div>
              )}
            </div>
            {/* Transaction ID Input */}
            <div className="mb-4 flex flex-col items-center">
              <label htmlFor="transactionId" className="text-white font-semibold mb-1">Enter Transaction/UTR ID</label>
              <input
                id="transactionId"
                type="text"
                value={transactionId}
                onChange={e => setTransactionId(e.target.value)}
                className="px-3 py-2 rounded bg-gray-800 text-white border border-gray-600 w-64 text-center"
                placeholder="e.g. 1234567890UTR"
              />
            </div>
            <div className="flex flex-col items-center gap-2 mb-2">
              <div>
                <div className="font-semibold text-white mb-1">Your Registration QR Code</div>
                <QRCodeCanvas
                  id="registration-qr"
                  value={`Registration ID: ${registrationId}\nName: ${personName}\nTimestamp: ${timestamp}\nTransaction/UTR ID: ${transactionId}`}
                  size={180}
                  bgColor="#fff"
                  fgColor="#222"
                  level="H"
                  includeMargin={true}
                />
                <Button onClick={() => downloadQRCodeById('registration-qr', `Registration_QR_${registrationId}.png`)} className="mt-2 flex items-center gap-2">
                  <QrCode className="w-4 h-4" /> Download QR Code
                </Button>
              </div>
            </div>
            <div className="text-sm text-gray-400">Registration Time: {timestamp}</div>
          </motion.div>

          {/* Payment Proof & Verification Instructions */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-8"
          >
            <Card className="bg-gray-900/50 border-red-500/30">
              <CardHeader>
                <CardTitle className="text-white flex items-center gap-2">
                  <Mail className="w-5 h-5 text-red-400" />
                  Important Next Step
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-left text-gray-300 space-y-2">
                  <p>
                    <b>To complete your registration:</b> <br />
                    <span className="text-green-400">Send your payment proof (screenshot/receipt) and your transaction ID</span> to <a href="mailto:<EMAIL>" className="text-blue-400 underline"><EMAIL></a> <b>with your Registration ID above</b>.
                  </p>
                  <p>
                    Our team will verify your payment and confirm your spot. <br />
                    <span className="text-yellow-400">You must complete this step to attend the workshop.</span>
                  </p>
                  <p>
                    <b>Note:</b> Please keep your Registration ID and transaction ID safe. You will need to show them to the registration team on the workshop day.
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Workshop Details Card */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6 }}
            className="mb-8"
          >
            <Card className="bg-gray-900/50 border-gray-700">
              <CardHeader>
                <CardTitle className="text-white flex items-center justify-center gap-2">
                  <Calendar className="w-5 h-5 text-green-400" />
                  Workshop Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Calendar className="w-4 h-4 text-blue-400" />
                      <div>
                        <p className="text-white font-semibold">Duration</p>
                        <p className="text-gray-300">2 Days Workshop</p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <MapPin className="w-4 h-4 text-purple-400" />
                      <div>
                        <p className="text-white font-semibold">Venue</p>
                        <p className="text-gray-300">SKP Engineering College</p>
                        <p className="text-sm text-gray-400">Tiruvannamalai - 606 611</p>
                      </div>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      <Mail className="w-4 h-4 text-green-400" />
                      <div>
                        <p className="text-white font-semibold">Contact Email</p>
                        <p className="text-gray-300"><EMAIL></p>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="w-4 h-4 text-yellow-400" />
                      <div>
                        <p className="text-white font-semibold">Phone</p>
                        <p className="text-gray-300">6374344424</p>
                        <p className="text-gray-300">9790155280</p>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Next Steps */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="mb-8"
          >
            <Card className="bg-blue-900/20 border-blue-500/30">
              <CardHeader>
                <CardTitle className="text-white">What's Next?</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3 text-left">
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">1</div>
                    <p className="text-gray-300">Send your payment proof and transaction ID to <span className="text-blue-400"><EMAIL></span> with your Registration ID.</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold">2</div>
                    <p className="text-gray-300">Wait for our team to verify your payment and confirm your spot.</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center text-white text-sm font-bold">3</div>
                    <p className="text-gray-300">You will receive workshop joining instructions and the official start date by email.</p>
                  </div>
                  <div className="flex items-start gap-3">
                    <div className="w-6 h-6 bg-yellow-500 rounded-full flex items-center justify-center text-white text-sm font-bold">4</div>
                    <p className="text-gray-300">Bring your Registration ID and payment transaction ID on the workshop day for entry.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 1.0 }}
            className="flex flex-col sm:flex-row gap-4 justify-center"
          >
            <Link to="/">
              <Button size="lg" className="button-gradient">
                <Home className="mr-2 w-4 h-4" />
                Back to Home
              </Button>
            </Link>
            <Button size="lg" variant="outline" className="border-green-400 text-green-400 hover:bg-green-400 hover:text-white" onClick={downloadQRCode}>
              <Download className="mr-2 w-4 h-4" />
              Download QR
            </Button>
          </motion.div>

          {/* Contact Support */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 1.2 }}
            className="mt-8 text-center"
          >
            <p className="text-gray-400 text-sm">
              Need help? Contact us at{" "}
              <a href="mailto:<EMAIL>" className="text-green-400 hover:underline">
                <EMAIL>
              </a>
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default RegistrationSuccess;

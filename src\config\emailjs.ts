// EmailJS Configuration
// Replace these with your actual EmailJS credentials

export const EMAILJS_CONFIG = {
  SERVICE_ID: 'service_vf9jlhp', // Replace with your EmailJS service ID
  TEMPLATE_ID: 'template_5iawa8h', // Replace with your EmailJS template ID
  PUBLIC_KEY: 'MYctfRKETJaclx6mn', // Replace with your EmailJS public key
};

// EmailJS Template Variables
export const EMAIL_TEMPLATE_PARAMS = {
  to_email: '<EMAIL>',
  from_name: '{{from_name}}',
  from_email: '{{from_email}}',
  phone: '{{phone}}',
  college: '{{college}}',
  year: '{{year}}',
  branch: '{{branch}}',
  experience: '{{experience}}',
  expectations: '{{expectations}}',
  transaction_id: '{{transaction_id}}',
  utr_code: '{{utr_code}}',
  payment_method: '{{payment_method}}',
  registration_date: '{{registration_date}}',
};

// Instructions for EmailJS Setup:
/*
1. Go to https://www.emailjs.com/
2. Create an account and verify your email
3. Create a new service (Gmail, Outlook, etc.)
4. Create a new email template with the following variables:
   - {{from_name}}
   - {{from_email}}
   - {{phone}}
   - {{college}}
   - {{year}}
   - {{branch}}
   - {{experience}}
   - {{expectations}}
   - {{transaction_id}}
   - {{utr_code}}
   - {{payment_method}}
   - {{registration_date}}
5. Get your Service ID, Template ID, and Public Key
6. Replace the values in EMAILJS_CONFIG above
*/

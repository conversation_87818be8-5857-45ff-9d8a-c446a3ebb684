import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Users, Calendar, MapPin, Star } from "lucide-react";

const WorkshopImages = () => {
  const workshopImages = [
    {
      id: 1,
      image: "/images/WhatsApp Image 2025-06-24 at 7.50.34 PM.jpeg",
      title: "Hands-on Penetration Testing",
      date: "March 2024",
      participants: "50+ Students",
      summary: [
        "Students learned advanced penetration testing techniques using Kali Linux and industry-standard tools.",
        "Practical sessions covered vulnerability assessment, network scanning, and ethical hacking methodologies.",
        "Interactive workshops with real-world scenarios and live demonstrations by cybersecurity experts."
      ]
    },
    {
      id: 2,
      image: "/images/WhatsApp Image 2025-06-24 at 7.52.27 PM.jpeg",
      title: "CTF Competition & Training",
      date: "February 2024",
      participants: "75+ Participants",
      summary: [
        "Intensive Capture The Flag competition with challenges in web security, cryptography, and forensics.",
        "Teams competed in solving real-world cybersecurity puzzles and attack simulations.",
        "Winners received certificates and recognition for their outstanding problem-solving skills."
      ]
    },
    {
      id: 3,
      image: "/images/3drenderingcartoonlikemanworkingcomputer_884224-2312-Photoroom.png",
      title: "Web Application Security",
      date: "January 2024",
      participants: "60+ Developers",
      summary: [
        "Comprehensive training on OWASP Top 10 vulnerabilities and secure coding practices.",
        "Hands-on experience with Burp Suite, OWASP ZAP, and other web security testing tools.",
        "Practical exercises in identifying and mitigating SQL injection, XSS, and CSRF attacks."
      ]
    },
    {
      id: 4,
      image: "/images/man-sits-laptop-with-word-web-screen_1181564-2976-Photoroom.png",
      title: "Network Security Fundamentals",
      date: "December 2023",
      participants: "40+ IT Professionals",
      summary: [
        "Deep dive into network protocols, firewalls, and intrusion detection systems.",
        "Practical sessions on Wireshark for network traffic analysis and threat detection.",
        "Implementation of security policies and best practices for enterprise networks."
      ]
    },
    {
      id: 5,
      image: "/images/certificate.png",
      title: "Cybersecurity Certification Program",
      date: "November 2023",
      participants: "100+ Graduates",
      summary: [
        "Comprehensive 6-month certification program covering all aspects of cybersecurity.",
        "Industry-recognized certification with placement assistance and career guidance.",
        "Alumni network of certified professionals working in top cybersecurity companies."
      ]
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-b from-black via-gray-900 to-black relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-10 w-32 h-32 bg-blue-500/10 rounded-full blur-xl animate-pulse"></div>
        <div className="absolute bottom-20 right-10 w-40 h-40 bg-green-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
      </div>

      <div className="container px-4 relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-16"
        >
          <motion.div
            initial={{ scale: 0.8, opacity: 0 }}
            whileInView={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="inline-block mb-6"
          >
            <div className="glass rounded-full px-6 py-2 border border-white/20">
              <span className="text-sm font-medium text-blue-400">
                📸 Previous Workshops
              </span>
            </div>
          </motion.div>

          <h2 className="text-4xl md:text-6xl font-bold mb-6">
            <span className="bg-gradient-to-r from-white via-gray-200 to-white bg-clip-text text-transparent">
              Success Stories &
            </span>
            <br />
            <span className="bg-gradient-to-r from-blue-400 via-blue-500 to-blue-600 bg-clip-text text-transparent">
              Workshop Gallery
            </span>
          </h2>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-xl text-gray-300 max-w-3xl mx-auto"
          >
            Explore our previous cybersecurity workshops and see the impact we've made in training the next generation of security professionals.
          </motion.p>
        </motion.div>

        {/* Workshop Images Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto">
          {workshopImages.map((workshop, index) => (
            <motion.div
              key={workshop.id}
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              className="group"
            >
              <Card className="glass-enhanced hover:border-blue-400/50 transition-all duration-300 h-full shadow-lg hover:shadow-blue-500/15 overflow-hidden">
                <CardContent className="p-0">
                  {/* Workshop Image */}
                  <div className="relative overflow-hidden">
                    <img
                      src={workshop.image}
                      alt={workshop.title}
                      className="w-full h-48 sm:h-56 object-cover transition-transform duration-300 group-hover:scale-105"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent"></div>
                    
                    {/* Workshop Stats Overlay */}
                    <div className="absolute bottom-4 left-4 right-4">
                      <div className="flex flex-wrap gap-2">
                        <Badge className="bg-blue-500/80 text-white text-xs">
                          <Calendar className="w-3 h-3 mr-1" />
                          {workshop.date}
                        </Badge>
                        <Badge className="bg-green-500/80 text-white text-xs">
                          <Users className="w-3 h-3 mr-1" />
                          {workshop.participants}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  {/* Workshop Details */}
                  <div className="p-6">
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-blue-400 transition-colors">
                      {workshop.title}
                    </h3>

                    {/* Summary Points */}
                    <div className="space-y-2 mb-4">
                      {workshop.summary.map((point, pointIndex) => (
                        <div key={pointIndex} className="flex items-start gap-2">
                          <Star className="w-3 h-3 text-yellow-400 mt-1 flex-shrink-0" />
                          <p className="text-gray-300 text-sm leading-relaxed">
                            {point}
                          </p>
                        </div>
                      ))}
                    </div>

                    {/* Workshop Location */}
                    <div className="flex items-center gap-2 text-gray-400 text-sm">
                      <MapPin className="w-4 h-4" />
                      <span>SKP Engineering College</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="glass-enhanced border-blue-500/30 rounded-2xl p-8 max-w-4xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              🚀 Ready to Join Our Next Workshop?
            </h3>
            <p className="text-gray-300 mb-6">
              Don't miss out on the opportunity to be part of our next cybersecurity workshop. 
              Join hundreds of students who have already transformed their careers!
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <a
                  href="#register"
                  className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg hover:shadow-blue-500/25 transition-all duration-300"
                >
                  <Calendar className="mr-2 w-5 h-5" />
                  Register for Next Workshop
                </a>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default WorkshopImages;

import { motion } from "framer-motion";
import { Shield, Lock, Code2, Zap } from "lucide-react";

interface FeatureContentProps {
  image: string;
  title: string;
}

export const FeatureContent = ({ image, title }: FeatureContentProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="h-full flex items-center justify-center"
    >
      <div className="relative w-full h-[250px] sm:h-[300px] md:h-[350px] lg:h-[400px] rounded-xl md:rounded-2xl overflow-hidden group shadow-2xl">
        {/* Background Image */}
        <div className="absolute inset-0">
          <img
            src={image}
            alt={title}
            className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-105 md:group-hover:scale-110"
            loading="lazy"
          />
          {/* Overlay */}
          <div className="absolute inset-0 bg-gradient-to-br from-black/70 via-red-900/40 to-blue-900/50"></div>
          <div className="absolute inset-0 bg-gradient-to-t from-black/90 via-black/20 to-transparent"></div>
        </div>

        {/* Content Overlay */}
        <div className="relative z-10 h-full flex flex-col justify-end p-4 sm:p-6 md:p-8">
          {/* Floating Icons */}
          <div className="absolute top-3 right-3 sm:top-4 sm:right-4 md:top-6 md:right-6 flex gap-2 md:gap-3">
            <div className="p-1.5 md:p-2 bg-red-500/20 backdrop-blur-sm rounded-md md:rounded-lg border border-red-500/30">
              <Shield className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-red-400" />
            </div>
            <div className="p-1.5 md:p-2 bg-blue-500/20 backdrop-blur-sm rounded-md md:rounded-lg border border-blue-500/30">
              <Lock className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 text-blue-400" />
            </div>
          </div>

          {/* Title and Description */}
          <div className="space-y-2 md:space-y-4">
            <h3 className="text-lg sm:text-xl md:text-2xl font-bold text-white mb-1 md:mb-2 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
              {title}
            </h3>

            {/* Feature Highlights */}
            <div className="space-y-1.5 md:space-y-2">
              {title === "Hands-on CTF Training" && (
                <>
                  <div className="flex items-center gap-1.5 md:gap-2 text-green-400">
                    <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Real-world challenges</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2 text-blue-400">
                    <Code2 className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Interactive scenarios</span>
                  </div>
                </>
              )}

              {title === "Cybersecurity Fundamentals" && (
                <>
                  <div className="flex items-center gap-1.5 md:gap-2 text-red-400">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Threat analysis</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2 text-purple-400">
                    <Lock className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Defense strategies</span>
                  </div>
                </>
              )}

              {title === "Programming for Security" && (
                <>
                  <div className="flex items-center gap-1.5 md:gap-2 text-yellow-400">
                    <Code2 className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Multiple languages</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2 text-green-400">
                    <Zap className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Secure coding</span>
                  </div>
                </>
              )}

              {title === "Expert Mentorship" && (
                <>
                  <div className="flex items-center gap-1.5 md:gap-2 text-blue-400">
                    <Shield className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Industry experts</span>
                  </div>
                  <div className="flex items-center gap-1.5 md:gap-2 text-cyan-400">
                    <Lock className="w-3 h-3 sm:w-4 sm:h-4" />
                    <span className="text-xs sm:text-sm">Personal guidance</span>
                  </div>
                </>
              )}
            </div>

            {/* Progress Bar */}
            <div className="w-full bg-white/10 rounded-full h-1.5 md:h-2 mt-2 md:mt-4">
              <div className="bg-gradient-to-r from-red-500 to-blue-500 h-1.5 md:h-2 rounded-full w-3/4 animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* Glass Border Effect */}
        <div className="absolute inset-0 rounded-2xl border border-white/10 pointer-events-none"></div>
      </div>
    </motion.div>
  );
};